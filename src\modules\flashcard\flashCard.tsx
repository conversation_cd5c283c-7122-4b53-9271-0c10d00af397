/* eslint-disable react-native/no-inline-styles */
import {
  RefreshControl,
  Text,
  TouchableOpacity,
  View,
  Image,
  FlatList,
  Dimensions,
  SafeAreaView,
} from 'react-native';
import React, {
  useEffect,
  useRef,
  useState,
  useCallback,
  useMemo,
  forwardRef,
} from 'react';
import {useTranslation} from 'react-i18next';
import {
  FBottomSheet,
  Winicon,
  showSnackbar,
  ComponentStatus,
  TextField,
  showBottomSheet,
  Checkbox,
  AppButton,
  hideBottomSheet,
  ListTile,
} from 'wini-mobile-components';
import {TabView, TabBar} from 'react-native-tab-view';
import {ColorThemes} from '../../assets/skin/colors';
import {navigate, RootScreen} from '../../router/router';
import EmptyPage from '../../Screen/emptyPage';
import {flashCardDA} from './da';
import {useNavigation} from '@react-navigation/native';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import {useSelectorCustomerState} from '../../redux/hook/customerHook';
import {ScrollView} from 'react-native-gesture-handler';
import {TypoSkin} from '../../assets/skin/typography';
import {CustomerDA} from '../customer/da';
import ConfigAPI from '../../Config/ConfigAPI';
import WScreenFooter from '../../Screen/Layout/footer';
import ScreenHeader from '../../Screen/Layout/header';
import FastImage from 'react-native-fast-image';

// Component tìm kiếm và bộ lọc
const SearchAndFilter = ({
  searchValue,
  onSearchChange,
  selectedTopics,
  onTopicChange,
  topics,
  isLoadingTopics,
  t,
}: {
  searchValue: string;
  onSearchChange: (value: string) => void;
  selectedTopics: string[];
  onTopicChange: (value: string[]) => void;
  topics: any[];
  isLoadingTopics: boolean;
  t: any;
}) => {
  const bottomSheetRef = useRef<any>(null);

  const topicOptions = useMemo(() => {
    return topics.map(topic => ({
      Id: topic.Id,
      Name: topic.Name,
    }));
  }, [topics]);

  const getSelectedTopicNames = () => {
    if (selectedTopics.length === 0) return t('flashcard.allTopics');
    return `${selectedTopics.length} ${t('flashcard.topicsSelected')}`;
  };

  return (
    <View
      style={{
        paddingHorizontal: 16,
        paddingVertical: 12,
        backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
        borderBottomWidth: 1,
        borderBottomColor: ColorThemes.light.Neutral_Border_Color_Main,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        gap: 8,
      }}>
      <FBottomSheet ref={bottomSheetRef} />

      {/* Search Input */}
      <TextField
        style={{paddingHorizontal: 16, height: 40, width: '70%'}}
        onChange={onSearchChange}
        value={searchValue}
        placeholder={t('flashcard.searchPlaceholder')}
        prefix={
          <Winicon
            src="outline/development/zoom"
            size={14}
            color={ColorThemes.light.Neutral_Text_Color_Subtitle}
          />
        }
        suffix={
          searchValue ? (
            <TouchableOpacity
              onPress={() => onSearchChange('')}
              style={{padding: 4}}>
              <Winicon
                src="outline/layout/xmark"
                size={14}
                color={ColorThemes.light.Neutral_Text_Color_Subtitle}
              />
            </TouchableOpacity>
          ) : null
        }
      />
      <TouchableOpacity
        onPress={() => {
          showBottomSheet({
            ref: bottomSheetRef,
            enableDismiss: true,
            children: (
              <PopupFilter
                ref={bottomSheetRef}
                topicOptions={topicOptions}
                selectedTopics={selectedTopics}
                onApply={onTopicChange}
              />
            ),
          });
        }}
        style={{
          backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
          borderRadius: 8,
          padding: 8,
          paddingHorizontal: 12,
          flexDirection: 'row',
          alignItems: 'center',
          flex: 1,
          width: '25%',
          gap: 6,
        }}>
        <Winicon
          src="fill/user interface/filter"
          size={16}
          color={
            selectedTopics.length
              ? ColorThemes.light.Info_Color_Main
              : ColorThemes.light.Neutral_Text_Color_Subtitle
          }
        />
        <Text
          style={{
            ...TypoSkin.subtitle3,
            fontSize: 15,
            color: selectedTopics.length
              ? ColorThemes.light.Info_Color_Main
              : ColorThemes.light.Neutral_Text_Color_Subtitle,
          }}
          numberOfLines={1}>
          {getSelectedTopicNames()}
        </Text>
      </TouchableOpacity>
    </View>
  );
};
// create PopupFilter with data
const PopupFilter = forwardRef(function PopupFilter(
  data: {
    topicOptions: any[];
    selectedTopics: string[];
    onApply: (values: string[]) => void;
  },
  ref: any,
) {
  const {topicOptions, selectedTopics, onApply} = data;
  const [selected, setSelected] = useState<string[]>([]);

  useEffect(() => {
    setSelected(selectedTopics || []);
  }, [selectedTopics]);

  return (
    <SafeAreaView
      style={{
        width: '100%',
        height: Dimensions.get('window').height / 1.5,
        borderTopLeftRadius: 12,
        borderTopRightRadius: 12,
        backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
      }}>
      <ScreenHeader
        style={{
          backgroundColor: ColorThemes.light.transparent,
          borderBottomColor: ColorThemes.light.Neutral_Background_Color_Main,
          borderBottomWidth: 0.5,
          shadowColor: 'rgba(0, 0, 0, 0.03)',
          shadowOffset: {
            width: 0,
            height: 4,
          },
          shadowRadius: 20,
          elevation: 20,
          shadowOpacity: 1,
        }}
        title={'Bộ lọc'}
        prefix={<View style={{width: 50}} />}
        action={
          <TouchableOpacity
            onPress={() => hideBottomSheet(ref)}
            style={{flexDirection: 'row', padding: 16, alignItems: 'center'}}>
            <Winicon
              src="outline/layout/xmark"
              size={20}
              color={ColorThemes.light.Neutral_Text_Color_Body}
            />
          </TouchableOpacity>
        }
      />
      <View style={{flex: 1, paddingHorizontal: 16, paddingVertical: 16}}>
        <FlatList
          data={topicOptions}
          ListHeaderComponent={() => {
            return (
              <Text style={{...TypoSkin.label3, marginBottom: 8}}>
                Loại chủ đề
              </Text>
            );
          }}
          renderItem={({item}: {item: any; index: number}) => (
            <ListTile
              title={item.Name?.trim()}
              style={{
                borderColor: ColorThemes.light.Neutral_Border_Color_Main,
                borderWidth: 1,
                padding: 0,
              }}
              listtileStyle={{padding: 8}}
              onPress={() => {
                if (selected.includes(item.Id)) {
                  setSelected(selected.filter((id: any) => id !== item.Id));
                } else {
                  setSelected([...selected, item.Id]);
                }
              }}
              trailing={
                <Checkbox
                  value={selected.includes(item.Id)}
                  onChange={(v: any) => {
                    if (v) setSelected([...selected, item.Id]);
                    else
                      setSelected(selected.filter((id: any) => id !== item.Id));
                  }}
                />
              }
            />
          )}
          style={{width: '100%', height: '100%'}}
          contentContainerStyle={{gap: 16}}
          keyExtractor={(_, index) => index.toString()}
        />
      </View>
      <WScreenFooter
        style={{flexDirection: 'row', gap: 8, paddingHorizontal: 16}}>
        <AppButton
          title={'Làm mới'}
          backgroundColor={ColorThemes.light.Neutral_Background_Color_Main}
          borderColor="transparent"
          containerStyle={{
            height: 40,
            flex: 1,
            borderRadius: 8,
            paddingHorizontal: 12,
            paddingVertical: 5,
          }}
          onPress={() => {
            setSelected([]);
          }}
          textColor={ColorThemes.light.Neutral_Text_Color_Subtitle}
        />
        <AppButton
          title={'Áp dụng'}
          backgroundColor={ColorThemes.light.Primary_Color_Main}
          borderColor="transparent"
          containerStyle={{
            height: 40,
            flex: 1,
            borderRadius: 8,
            paddingHorizontal: 12,
            paddingVertical: 5,
          }}
          onPress={() => {
            hideBottomSheet(ref);
            onApply(selected);
          }}
          textColor={ColorThemes.light.Neutral_Background_Color_Absolute}
        />
      </WScreenFooter>
    </SafeAreaView>
  );
});

// Component cho tab FlashCard phổ biến
const PublicFlashCardsTab = ({
  isLoading,
  data,
  isRefresh,
  onRefresh,
  customerInfo,
  customer,
  searchValue,
  onSearchChange,
  selectedTopics,
  onTopicChange,
  topics,
  isLoadingTopics,
  t,
}: {
  isLoading: boolean;
  data: any[];
  isRefresh: boolean;
  onRefresh: () => void;
  customerInfo: Map<string, any>;
  customer: any;
  searchValue: string;
  onSearchChange: (value: string) => void;
  selectedTopics: string[];
  onTopicChange: (value: string[]) => void;
  topics: any[];
  isLoadingTopics: boolean;
  t: any;
}) => (
  <View style={{flex: 1}}>
    <SearchAndFilter
      searchValue={searchValue}
      onSearchChange={onSearchChange}
      selectedTopics={selectedTopics}
      onTopicChange={onTopicChange}
      topics={topics}
      isLoadingTopics={isLoadingTopics}
      t={t}
    />
    {isLoading ? (
      <View style={{gap: 16, paddingTop: 16}}>
        {[1, 2, 3, 4].map((_, idx) => (
          <SkeletonFlashCard key={`skeleton-public-${idx}`} />
        ))}
      </View>
    ) : data?.length ? (
      <ScrollView
        style={{paddingTop: 16}}
        contentContainerStyle={{paddingBottom: 100}}
        refreshControl={
          <RefreshControl refreshing={isRefresh} onRefresh={onRefresh} />
        }>
        <View style={{gap: 16}}>
          {data.map((item: any, idx: number) => (
            <FlashCardItem
              key={idx}
              item={item}
              index={idx}
              customerInfo={customerInfo}
              customer={customer}
              t={t}
            />
          ))}
        </View>
      </ScrollView>
    ) : (
      <EmptyPage title={t('common.noData')} />
    )}
  </View>
);

// Component cho tab FlashCard của tôi
const MyFlashCardsTab = ({
  isLoading,
  data,
  isRefresh,
  onRefresh,
  customerInfo,
  customer,
  onEdit,
  onDelete,
  searchValue,
  onSearchChange,
  selectedTopics,
  onTopicChange,
  topics,
  isLoadingTopics,
  t,
}: {
  isLoading: boolean;
  data: any[];
  isRefresh: boolean;
  onRefresh: () => void;
  customerInfo: Map<string, any>;
  customer: any;
  onEdit?: (item: any) => void;
  onDelete?: (item: any) => void;
  searchValue: string;
  onSearchChange: (value: string) => void;
  selectedTopics: string[];
  onTopicChange: (value: string[]) => void;
  topics: any[];
  isLoadingTopics: boolean;
  t: any;
}) => (
  <View style={{flex: 1}}>
    <SearchAndFilter
      searchValue={searchValue}
      onSearchChange={onSearchChange}
      selectedTopics={selectedTopics}
      onTopicChange={onTopicChange}
      topics={topics}
      isLoadingTopics={isLoadingTopics}
      t={t}
    />
    {isLoading ? (
      <View style={{gap: 16, paddingTop: 16}}>
        {[1, 2, 3, 4].map((_, idx) => (
          <SkeletonFlashCard key={`skeleton-my-${idx}`} />
        ))}
      </View>
    ) : data?.length ? (
      <ScrollView
        style={{paddingTop: 16}}
        contentContainerStyle={{paddingBottom: 100}}
        refreshControl={
          <RefreshControl refreshing={isRefresh} onRefresh={onRefresh} />
        }>
        <View style={{gap: 16}}>
          {data.map((item: any, idx: number) => (
            <FlashCardItem
              key={idx}
              item={item}
              index={idx}
              customerInfo={customerInfo}
              customer={customer}
              onEdit={onEdit}
              onDelete={onDelete}
              t={t}
            />
          ))}
        </View>
      </ScrollView>
    ) : (
      <EmptyPage title={t('common.noData')} />
    )}
  </View>
);

// Component FlashCard Item tùy chỉnh
const FlashCardItem = ({
  item,
  index,
  customerInfo,
  customer,
  onEdit,
  onDelete,
  t,
}: {
  item: any;
  index: number;
  customerInfo: Map<string, any>;
  customer: any;
  onEdit?: (item: any) => void;
  onDelete?: (item: any) => void;
  t: any;
}) => {
  const creatorInfo = customerInfo.get(item.CustomerId);
  const isMyCard = customer.Id === item.CustomerId;

  const renderSmallAvatar = () => {
    if (creatorInfo?.AvatarUrl) {
      return (
        <FastImage
          source={{
            uri: creatorInfo.AvatarUrl.includes('http')
              ? creatorInfo.AvatarUrl
              : ConfigAPI.getValidLink(creatorInfo.AvatarUrl),
          }}
          style={{
            width: 16,
            height: 16,
            borderRadius: 8,
            backgroundColor: ColorThemes.light.Neutral_Border_Color_Main,
          }}
        />
      );
    } else {
      return (
        <View
          style={{
            width: 16,
            height: 16,
            borderRadius: 8,
            backgroundColor: ColorThemes.light.Primary_Color_Main,
            justifyContent: 'center',
            alignItems: 'center',
          }}>
          <Text
            style={{
              fontSize: 8,
              fontWeight: '500',
              color: ColorThemes.light.Neutral_Background_Color_Absolute,
            }}>
            {creatorInfo?.Name ? creatorInfo.Name.charAt(0).toUpperCase() : 'U'}
          </Text>
        </View>
      );
    }
  };

  return (
    <TouchableOpacity
      key={index}
      onPress={() => {
        navigate(RootScreen.DeckDetail, {item});
      }}
      style={{
        borderColor: ColorThemes.light.Neutral_Border_Color_Main,
        borderWidth: 1,
        marginHorizontal: 16,
        padding: 16,
        backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
        borderRadius: 8,
        position: 'relative',
      }}>
      {/* Topic badge ở góc trên bên phải */}
      {item.Topic && (
        <View
          style={{
            position: 'absolute',
            top: 16,
            right: 16,
            backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
            paddingHorizontal: 8,
            paddingVertical: 4,
            borderRadius: 12,
            zIndex: 1,
          }}>
          <Text
            style={{
              ...TypoSkin.subtitle4,
              color: ColorThemes.light.Neutral_Text_Color_Subtitle,
            }}
            numberOfLines={1}>
            {item.Topic?.trim()}
          </Text>
        </View>
      )}

      <View style={{flexDirection: 'row', alignItems: 'flex-start', gap: 12}}>
        {/* Thông tin chính */}
        <View style={{flex: 1, gap: 8, paddingRight: item.Topic ? 60 : 0}}>
          {/* Tên flashcard */}
          <Text
            style={{
              ...TypoSkin.subtitle1,
              color: ColorThemes.light.Neutral_Text_Color_Title,
            }}>
            {item.Name.toString()}
          </Text>

          {/* Thông tin người tạo */}
          <View style={{flexDirection: 'row', alignItems: 'center', gap: 4}}>
            {renderSmallAvatar()}
            <Text
              style={{
                ...TypoSkin.subtitle4,
                color: ColorThemes.light.Neutral_Text_Color_Subtitle,
              }}>
              {creatorInfo?.Name || t('flashcard.defaultUser')}
            </Text>
          </View>

          {/* Số từ học */}
          <View style={{flexDirection: 'row', alignItems: 'center', gap: 4}}>
            <Winicon
              src="outline/files/document-copy"
              size={12}
              color={ColorThemes.light.Neutral_Text_Color_Subtitle}
            />
            <Text
              style={{
                ...TypoSkin.subtitle4,
                color: ColorThemes.light.Neutral_Text_Color_Subtitle,
                marginRight: 8,
              }}>
              {t('flashcard.wordsCount', {count: item.lstDetail?.length ?? 0})}
            </Text>
            {!isMyCard ? (
              <>
                <Winicon
                  src="outline/user interface/view"
                  size={12}
                  color={ColorThemes.light.Neutral_Text_Color_Subtitle}
                />
                <Text
                  style={{
                    ...TypoSkin.subtitle4,
                    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
                    marginRight: 8,
                  }}>
                  {t('flashcard.learnersCount', {count: item.Count ?? 0})}
                </Text>
              </>
            ) : null}
          </View>
        </View>

        {/* Action buttons - chỉ hiển thị cho FlashCard của tôi */}
        {isMyCard && (onEdit || onDelete) && (
          <View
            style={{
              flexDirection: 'row',
              gap: 8,
              alignItems: 'center',
              marginTop: item.Topic ? 24 : 0, // Offset để tránh topic badge
            }}>
            {onEdit && (
              <TouchableOpacity
                style={{
                  padding: 8,
                  borderRadius: 20,
                  // backgroundColor: ColorThemes.light.Primary_Color_Main,
                }}
                onPress={e => {
                  e.stopPropagation(); // Ngăn trigger onPress của TouchableOpacity cha
                  onEdit(item);
                }}>
                <Winicon
                  src="outline/user interface/edit"
                  color="#000"
                  size={16}
                />
              </TouchableOpacity>
            )}
            {onDelete && (
              <TouchableOpacity
                style={{
                  padding: 8,
                  borderRadius: 20,
                }}
                onPress={e => {
                  e.stopPropagation(); // Ngăn trigger onPress của TouchableOpacity cha
                  onDelete(item);
                }}>
                <Winicon
                  src="outline/user interface/trash-can"
                  color="red"
                  size={16}
                />
              </TouchableOpacity>
            )}
          </View>
        )}
      </View>
    </TouchableOpacity>
  );
};

export default function FlashCards() {
  const {t} = useTranslation();
  const [myCards, setMyCards] = useState<any[]>([]);
  const [publicCards, setPublicCards] = useState<any[]>([]);
  const [filteredMyCards, setFilteredMyCards] = useState<any[]>([]);
  const [filteredPublicCards, setFilteredPublicCards] = useState<any[]>([]);
  const [isLoadingMy, setLoadingMy] = useState(false);
  const [isLoadingPublic, setLoadingPublic] = useState(false);
  const [isRefreshMy, setRefreshMy] = useState(false);
  const [isRefreshPublic, setRefreshPublic] = useState(false);
  const [customerInfo, setCustomerInfo] = useState<Map<string, any>>(new Map());
  const [index, setIndex] = useState(0);
  const [routes] = useState([
    {key: 'public', title: t('flashcard.publicFlashCards')},
    {key: 'my', title: t('flashcard.myFlashCards')},
  ]);

  // Search and Filter states
  const [searchValue, setSearchValue] = useState('');
  const [selectedTopics, setSelectedTopics] = useState<string[]>([]);
  const [topics, setTopics] = useState<any[]>([]);
  const [isLoadingTopics, setLoadingTopics] = useState(false);

  const da = useMemo(() => new flashCardDA(), []);
  const customerDA = useMemo(() => new CustomerDA(), []);
  const bottomSheetRef = useRef<any>(null);
  const navigation = useNavigation<any>();
  const customer = useSelectorCustomerState().data;

  // Hàm lấy danh sách topics
  const getTopics = useCallback(async () => {
    setLoadingTopics(true);
    try {
      const result = await da.getTopic();
      if (result?.data) {
        setTopics(result.data);
      }
    } catch (error) {
      console.error('Error fetching topics:', error);
    }
    setLoadingTopics(false);
  }, [da]);

  // Handlers cho search và filter
  const handleSearchChange = useCallback((value: string) => {
    setSearchValue(value);
  }, []);

  const handleTopicChange = useCallback((topicIds: string[]) => {
    setSelectedTopics(topicIds);
  }, []);

  // Effect để gọi lại API khi search hoặc filter thay đổi
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      // Convert multiple topics to query string
      const topicQuery =
        selectedTopics.length > 0 ? selectedTopics.join('|') : '';
      getPublicData(searchValue, topicQuery);
      getMyData(searchValue, topicQuery);
    }, 500); // Debounce 500ms

    return () => clearTimeout(timeoutId);
  }, [searchValue, selectedTopics]); // eslint-disable-line react-hooks/exhaustive-deps

  // Cập nhật filtered data để hiển thị
  useEffect(() => {
    setFilteredPublicCards(publicCards);
    setFilteredMyCards(myCards);
  }, [publicCards, myCards]);

  // Hàm lấy dữ liệu FlashCard phổ biến
  const getPublicData = useCallback(
    async (search?: string, topicFilter?: string) => {
      setLoadingPublic(true);
      const result = await da.getListPublicFlashCard(
        undefined,
        undefined,
        search,
        topicFilter,
      );
      if (result) {
        const list = await Promise.all(
          result.data.map(async (item: any) => {
            const detail = await da.getListDetailbyId(item.Id);
            // Lấy topic name từ TopicId pattern
            const topicName =
              item.TopicId?.Name ||
              result.Topic?.find((t: any) => t.Id === item.TopicId)?.Name ||
              t('flashcard.unknownTopic');

            if (detail) {
              return {
                ...item,
                lstDetail: detail?.data,
                Topic: topicName,
              };
            } else {
              return {
                ...item,
                Topic: topicName,
              };
            }
          }),
        );

        // Lấy thông tin customer cho các flashcard
        const uniqueCustomerIds = [
          ...new Set(list.map((item: any) => item.CustomerId)),
        ];

        setCustomerInfo(prevCustomerInfo => {
          const newCustomerInfo = new Map(prevCustomerInfo);

          // Chỉ gọi API cho những customer chưa có thông tin
          const customerPromises = uniqueCustomerIds.map(
            async (customerId: string) => {
              if (customerId && !newCustomerInfo.has(customerId)) {
                const customerData = await customerDA.getCustomerItem(
                  customerId,
                );
                if (customerData) {
                  setCustomerInfo(current => {
                    const updated = new Map(current);
                    updated.set(customerId, customerData);
                    return updated;
                  });
                }
              }
            },
          );

          Promise.all(customerPromises);
          return newCustomerInfo;
        });

        setPublicCards(list);
      }
      setLoadingPublic(false);
      setRefreshPublic(false);
    },
    [da, customerDA, t],
  );

  // Hàm lấy dữ liệu FlashCard của tôi
  const getMyData = useCallback(
    async (search?: string, topicFilter?: string) => {
      setLoadingMy(true);
      const result = await da.getListMyFlashCard(
        undefined,
        undefined,
        search,
        topicFilter,
      );
      if (result) {
        const list = await Promise.all(
          result.data.map(async (item: any) => {
            const detail = await da.getListDetailbyId(item.Id);
            // Lấy topic name từ TopicId pattern
            const topicName =
              item.TopicId?.Name ||
              result.Topic?.find((t: any) => t.Id === item.TopicId)?.Name ||
              t('flashcard.unknownTopic');

            if (detail) {
              return {
                ...item,
                lstDetail: detail?.data,
                Topic: topicName,
              };
            } else {
              return {
                ...item,
                Topic: topicName,
              };
            }
          }),
        );

        // Lấy thông tin customer cho các flashcard
        const uniqueCustomerIds = [
          ...new Set(list.map((item: any) => item.CustomerId)),
        ];

        setCustomerInfo(prevCustomerInfo => {
          const newCustomerInfo = new Map(prevCustomerInfo);

          // Chỉ gọi API cho những customer chưa có thông tin
          const customerPromises = uniqueCustomerIds.map(
            async (customerId: string) => {
              if (customerId && !newCustomerInfo.has(customerId)) {
                const customerData = await customerDA.getCustomerItem(
                  customerId,
                );
                if (customerData) {
                  setCustomerInfo(current => {
                    const updated = new Map(current);
                    updated.set(customerId, customerData);
                    return updated;
                  });
                }
              }
            },
          );

          Promise.all(customerPromises);
          return newCustomerInfo;
        });

        setMyCards(list);
      }
      setLoadingMy(false);
      setRefreshMy(false);
    },
    [da, customerDA, t],
  );

  // Initial load - chỉ chạy 1 lần khi component mount
  useEffect(() => {
    getPublicData();
    getMyData();
    getTopics();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  // Focus listener - chỉ setup 1 lần
  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      // Chỉ refresh khi quay lại từ màn hình khác
      const topicQuery =
        selectedTopics.length > 0 ? selectedTopics.join('|') : '';
      getPublicData(searchValue, topicQuery);
      getMyData(searchValue, topicQuery);
    });

    // Cleanup function để remove listener khi component unmount
    return unsubscribe;
  }, [navigation, searchValue, selectedTopics]); // eslint-disable-line react-hooks/exhaustive-deps

  // Hàm refresh cho tab FlashCard phổ biến
  const onRefreshPublic = useCallback(async () => {
    setPublicCards([]);
    setCustomerInfo(new Map());
    setRefreshPublic(true);
    const topicQuery =
      selectedTopics.length > 0 ? selectedTopics.join('|') : '';
    await getPublicData(searchValue, topicQuery);
  }, [getPublicData, searchValue, selectedTopics]);

  // Hàm refresh cho tab FlashCard của tôi
  const onRefreshMy = useCallback(async () => {
    setMyCards([]);
    setCustomerInfo(new Map());
    setRefreshMy(true);
    const topicQuery =
      selectedTopics.length > 0 ? selectedTopics.join('|') : '';
    await getMyData(searchValue, topicQuery);
  }, [getMyData, searchValue, selectedTopics]);

  // Hàm xử lý sửa FlashCard
  const handleEditFlashCard = useCallback((item: any) => {
    navigate(RootScreen.CreateFlashCard, {
      id: item.Id,
    });
  }, []);

  // Hàm xử lý xóa FlashCard
  const handleDeleteFlashCard = useCallback(async (item: any) => {
    try {
      const result = await da.delete(item.Id);
      if (result) {
        showSnackbar({
          message: t('flashcard.deleteSuccess'),
          status: ComponentStatus.SUCCSESS,
        });
        // Refresh lại danh sách
        // await getMyData();
        setMyCards(prevCards => prevCards.filter(card => card.Id !== item.Id));
      } else {
        showSnackbar({
          message: t('flashcard.deleteError'),
          status: ComponentStatus.ERROR,
        });
      }
    } catch (error) {
      showSnackbar({
        message: t('flashcard.deleteError'),
        status: ComponentStatus.ERROR,
      });
    }
  }, []);

  const renderScene = useCallback(
    ({route: sceneRoute}: any) => {
      switch (sceneRoute.key) {
        case 'public':
          return (
            <PublicFlashCardsTab
              isLoading={isLoadingPublic}
              data={filteredPublicCards}
              isRefresh={isRefreshPublic}
              onRefresh={onRefreshPublic}
              customerInfo={customerInfo}
              customer={customer}
              searchValue={searchValue}
              onSearchChange={handleSearchChange}
              selectedTopics={selectedTopics}
              onTopicChange={handleTopicChange}
              topics={topics}
              isLoadingTopics={isLoadingTopics}
              t={t}
            />
          );
        case 'my':
          return (
            <MyFlashCardsTab
              isLoading={isLoadingMy}
              data={filteredMyCards}
              isRefresh={isRefreshMy}
              onRefresh={onRefreshMy}
              customerInfo={customerInfo}
              customer={customer}
              onEdit={handleEditFlashCard}
              onDelete={handleDeleteFlashCard}
              searchValue={searchValue}
              onSearchChange={handleSearchChange}
              selectedTopics={selectedTopics}
              onTopicChange={handleTopicChange}
              topics={topics}
              isLoadingTopics={isLoadingTopics}
              t={t}
            />
          );
        default:
          return null;
      }
    },
    [
      isLoadingPublic,
      filteredPublicCards,
      isRefreshPublic,
      onRefreshPublic,
      isLoadingMy,
      filteredMyCards,
      isRefreshMy,
      onRefreshMy,
      customerInfo,
      customer,
      handleEditFlashCard,
      handleDeleteFlashCard,
      searchValue,
      handleSearchChange,
      selectedTopics,
      handleTopicChange,
      topics,
      isLoadingTopics,
      t,
    ],
  );

  const renderTabBar = (props: any) => (
    <TabBar
      {...props}
      indicatorStyle={{
        backgroundColor: ColorThemes.light.Primary_Color_Main,
      }}
      style={{
        backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
        elevation: 0,
        shadowOpacity: 0,
        borderBottomWidth: 1,
        borderBottomColor: ColorThemes.light.Neutral_Border_Color_Main,
      }}
      labelStyle={{
        ...TypoSkin.subtitle3,
        textTransform: 'none',
      }}
      activeColor={ColorThemes.light.Primary_Color_Main}
      inactiveColor={ColorThemes.light.Neutral_Text_Color_Subtitle}
    />
  );

  return (
    <View
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
      }}>
      <FBottomSheet ref={bottomSheetRef} />

      <TabView
        navigationState={{index, routes}}
        renderScene={renderScene}
        renderTabBar={renderTabBar}
        onIndexChange={setIndex}
        initialLayout={{width: 400}}
      />

      {/* Floating Action Button */}
      <TouchableOpacity
        style={{
          position: 'absolute',
          bottom: 20,
          right: 20,
          width: 56,
          height: 56,
          borderRadius: 28,
          backgroundColor: ColorThemes.light.Primary_Color_Main,
          justifyContent: 'center',
          alignItems: 'center',
        }}
        onPress={() => {
          navigate(RootScreen.CreateFlashCard);
        }}>
        <Winicon
          src="outline/layout/plus"
          size={24}
          color={ColorThemes.light.Neutral_Background_Color_Absolute}
        />
      </TouchableOpacity>
    </View>
  );
}

const SkeletonFlashCard = () => {
  return (
    <SkeletonPlaceholder backgroundColor="#f0f0f0" highlightColor="#e0e0e0">
      <View
        style={{
          marginHorizontal: 16,
          marginBottom: 16,
          borderRadius: 8,
          borderWidth: 1,
          borderColor: '#e0e0e0',
          padding: 16,
        }}>
        {/* Title placeholder */}
        <View
          style={{
            width: '60%',
            height: 18,
            borderRadius: 4,
            marginBottom: 8,
          }}
        />

        {/* Subtitle/count placeholder */}
        <View
          style={{
            width: '30%',
            height: 14,
            borderRadius: 4,
          }}
        />

        {/* Action buttons placeholder - positioned to the right */}
        <View
          style={{
            position: 'absolute',
            right: 16,
            top: 16,
            flexDirection: 'row',
            gap: 8,
          }}>
          <View style={{width: 24, height: 24, borderRadius: 12}} />
          <View style={{width: 24, height: 24, borderRadius: 12}} />
        </View>
      </View>
    </SkeletonPlaceholder>
  );
};
