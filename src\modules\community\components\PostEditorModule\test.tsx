import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  FlatList,
  StyleSheet,
  Dimensions,
  Platform,
} from 'react-native';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

const MentionHashtagInput = () => {
  const [text, setText] = useState('');
  const [suggestions, setSuggestions] = useState([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [currentTrigger, setCurrentTrigger] = useState(null);
  const [triggerStartIndex, setTriggerStartIndex] = useState(-1);
  const [cursorPosition, setCursorPosition] = useState({ x: 0, y: 0 });
  const [textInputLayout, setTextInputLayout] = useState({ x: 0, y: 0, width: 0, height: 0 });
  const [selection, setSelection] = useState({ start: 0, end: 0 });
  
  const textInputRef = useRef(null);

  // Mock data
  const users = [
    { id: 1, name: '<PERSON>', username: 'johndo<PERSON>' },
    { id: 2, name: '<PERSON>', username: 'janesmith' },
    { id: 3, name: 'Mike <PERSON>', username: 'mikejohnson' },
  ];

  const hashtags = [
    { id: 1, tag: 'react' },
    { id: 2, tag: 'reactnative' },
    { id: 3, tag: 'javascript' },
  ];

  // Tính toán vị trí con trỏ
  const calculateCursorPosition = (textBeforeCursor, fontSize = 16, lineHeight = 20) => {
    const lines = textBeforeCursor.split('\n');
    const currentLineIndex = lines.length - 1;
    const currentLineText = lines[currentLineIndex] || '';
    
    // Tính toán X (horizontal position)
    // Ước tính độ rộng ký tự (có thể cần fine-tune)
    const avgCharWidth = fontSize * 0.6;
    const x = currentLineText.length * avgCharWidth;
    
    // Tính toán Y (vertical position)  
    const y = currentLineIndex * lineHeight;
    
    return { x: Math.min(x, SCREEN_WIDTH - 200), y };
  };

  const handleTextChange = (inputText) => {
    setText(inputText);
    
    const cursor = selection.start;
    
    // Tính toán vị trí con trỏ
    const textBeforeCursor = inputText.substring(0, cursor);
    const newCursorPosition = calculateCursorPosition(textBeforeCursor);
    setCursorPosition(newCursorPosition);
    
    // Logic tìm trigger và filter suggestions (giữ nguyên như trước)
    let triggerIndex = -1;
    let trigger = null;
    
    for (let i = cursor - 1; i >= 0; i--) {
      if (inputText[i] === '@' || inputText[i] === '#') {
        if (i === 0 || inputText[i - 1] === ' ' || inputText[i - 1] === '\n') {
          triggerIndex = i;
          trigger = inputText[i];
          break;
        }
      } else if (inputText[i] === ' ' || inputText[i] === '\n') {
        break;
      }
    }

    if (triggerIndex !== -1 && trigger) {
      const keyword = inputText.substring(triggerIndex + 1, cursor).toLowerCase();
      
      setCurrentTrigger(trigger);
      setTriggerStartIndex(triggerIndex);
      
      let filteredSuggestions = [];
      if (trigger === '@') {
        filteredSuggestions = users.filter(user => 
          user.username.toLowerCase().includes(keyword) || 
          user.name.toLowerCase().includes(keyword)
        );
      } else if (trigger === '#') {
        filteredSuggestions = hashtags.filter(hashtag => 
          hashtag.tag.toLowerCase().includes(keyword)
        );
      }
      
      setSuggestions(filteredSuggestions);
      setShowSuggestions(filteredSuggestions.length > 0);
    } else {
      setShowSuggestions(false);
      setSuggestions([]);
      setCurrentTrigger(null);
      setTriggerStartIndex(-1);
    }
  };

  const handleSelectionChange = (event) => {
    const { selection: newSelection } = event.nativeEvent;
    setSelection(newSelection);
    
    // Cập nhật vị trí con trỏ khi di chuyển cursor
    const textBeforeCursor = text.substring(0, newSelection.start);
    const newCursorPosition = calculateCursorPosition(textBeforeCursor);
    setCursorPosition(newCursorPosition);
  };

  const handleTextInputLayout = (event) => {
    const { layout } = event.nativeEvent;
    setTextInputLayout(layout);
  };

  const handleSuggestionPress = (item) => {
    const beforeTrigger = text.substring(0, triggerStartIndex);
    const afterCursor = text.substring(selection.start);
    
    let newText = '';
    if (currentTrigger === '@') {
      newText = `${beforeTrigger}@${item.username} ${afterCursor}`;
    } else if (currentTrigger === '#') {
      newText = `${beforeTrigger}#${item.tag} ${afterCursor}`;
    }
    
    setText(newText);
    setShowSuggestions(false);
    setSuggestions([]);
    setCurrentTrigger(null);
    setTriggerStartIndex(-1);
    
    textInputRef.current?.focus();
  };

  const renderSuggestionItem = ({ item }) => (
    <TouchableOpacity 
      style={styles.suggestionItem} 
      onPress={() => handleSuggestionPress(item)}
    >
      {currentTrigger === '@' ? (
        <View style={styles.userSuggestion}>
          <View style={styles.avatar}>
            <Text style={styles.avatarText}>
              {item.name.charAt(0).toUpperCase()}
            </Text>
          </View>
          <View style={styles.userInfo}>
            <Text style={styles.userName}>{item.name}</Text>
            <Text style={styles.userUsername}>@{item.username}</Text>
          </View>
        </View>
      ) : (
        <View style={styles.hashtagSuggestion}>
          <Text style={styles.hashtagIcon}>#</Text>
          <Text style={styles.hashtagText}>{item.tag}</Text>
        </View>
      )}
    </TouchableOpacity>
  );

  // Tính toán vị trí cuối cùng của suggestions dropdown
  const getSuggestionsStyle = () => {
    const baseTop = textInputLayout.y + cursorPosition.y + 25; // +25 để cách con trỏ 1 chút
    const baseLeft = textInputLayout.x + cursorPosition.x;
    
    // Đảm bảo không bị tràn màn hình
    const adjustedLeft = Math.max(10, Math.min(baseLeft, SCREEN_WIDTH - 250));
    const adjustedTop = baseTop > SCREEN_HEIGHT - 200 ? baseTop - 150 : baseTop;
    
    return {
      position: 'absolute',
      top: adjustedTop,
      left: adjustedLeft,
      width: 240,
      maxHeight: 150,
      backgroundColor: 'white',
      borderRadius: 8,
      borderWidth: 1,
      borderColor: '#e0e0e0',
      ...Platform.select({
        ios: {
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.25,
          shadowRadius: 4,
        },
        android: {
          elevation: 5,
        },
      }),
      zIndex: 1000,
    };
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Mention & Hashtag Input</Text>
      
      <View style={styles.inputWrapper}>
        <TextInput
          ref={textInputRef}
          style={styles.textInput}
          value={text}
          onChangeText={handleTextChange}
          onSelectionChange={handleSelectionChange}
          onLayout={handleTextInputLayout}
          placeholder="Gõ @ để mention hoặc # để hashtag..."
          multiline
          textAlignVertical="top"
        />
        
        {/* Suggestions dropdown tại vị trí con trỏ */}
        {showSuggestions && (
          <View style={getSuggestionsStyle()}>
            <View style={styles.suggestionsHeader}>
              <Text style={styles.suggestionsHeaderText}>
                {currentTrigger === '@' ? '👥 Mention' : '🏷️ Hashtag'}
              </Text>
            </View>
            <FlatList
              data={suggestions}
              renderItem={renderSuggestionItem}
              keyExtractor={(item) => item.id.toString()}
              keyboardShouldPersistTaps="handled"
              showsVerticalScrollIndicator={false}
            />
          </View>
        )}
      </View>

      {/* Debug info */}
      <View style={styles.debugInfo}>
        <Text style={styles.debugText}>
          Cursor Position: x={cursorPosition.x.toFixed(0)}, y={cursorPosition.y.toFixed(0)}
        </Text>
        <Text style={styles.debugText}>
          Selection: start={selection.start}, end={selection.end}
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
    color: '#333',
  },
  inputWrapper: {
    position: 'relative',
    marginBottom: 20,
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 15,
    fontSize: 16,
    backgroundColor: 'white',
    minHeight: 120,
    maxHeight: 200,
    lineHeight: 20, // Quan trọng cho tính toán vị trí
  },
  suggestionsHeader: {
    backgroundColor: '#f8f9fa',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  suggestionsHeaderText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#666',
  },
  suggestionItem: {
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  userSuggestion: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#007AFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  avatarText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
  },
  userUsername: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  hashtagSuggestion: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  hashtagIcon: {
    fontSize: 16,
    fontWeight: '600',
    color: '#34C759',
    marginRight: 8,
  },
  hashtagText: {
    fontSize: 14,
    color: '#34C759',
    fontWeight: '500',
  },
  debugInfo: {
    backgroundColor: 'white',
    padding: 10,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  debugText: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
});

export default MentionHashtagInput;