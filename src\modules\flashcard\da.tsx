import {DataController} from '../../base/baseController';
import store from '../../redux/store/store';
import {randomGID} from '../../utils/Utils';

export class flashCardDA {
  private flashcardController: DataController;
  private flashcardDetailController: DataController;
  private customerFlascardController: DataController;
  private topicController: DataController;

  constructor() {
    this.flashcardController = new DataController('FlashCard');
    this.flashcardDetailController = new DataController('FlashCardDetail');
    this.customerFlascardController = new DataController('CustomerFlashCard');
    this.topicController = new DataController('Topic');
  }
  async getListPublicFlashCard(
    page?: number,
    size?: number,
    searchQuery?: string,
    topicId?: string,
  ) {
    var cusId = store.getState().customer.data?.Id;
    if (cusId) {
      let query = `@IsPublic: {true}`;

      // Add search query if provided
      if (searchQuery && searchQuery.trim()) {
        query += ` @Name: *${searchQuery.trim()}*`;
      }

      // Add topic filter if provided (support multiple topics)
      if (topicId && topicId.trim()) {
        const topicIds = topicId.split('|').filter(id => id.trim());
        if (topicIds.length > 0) {
          query += ` @TopicId: {${topicIds.join(' | ')}}`;
        }
      }

      const response = await this.flashcardController.getPatternList({
        query: query,
        page: page,
        size: size,
        pattern: {
          TopicId: ['Id', 'Name'],
        },
      });
      if (response?.code === 200) {
        const listCardId = response.data.map((item: any) => item.Id);

        if (listCardId.length > 0) {
          const listCustomerCard = await this.customerFlascardController.group({
            reducers: `GROUPBY 1 @FlashCardId REDUCE COUNT 0 AS Count`,
            searchRaw: `@FlashCardId: {${listCardId.join(' | ')}}`,
          });
          if (listCustomerCard?.code === 200) {
            response.data = response.data.map((item: any) => {
              const customerCard = listCustomerCard.data.find(
                (a: any) => a.FlashCardId === item.Id,
              );
              return {
                ...item,
                Count: customerCard?.Count ?? 0,
              };
            });
          }
        }
        return response;
      }
    }
    return null;
  }
  async getListMyFlashCard(
    page?: number,
    size?: number,
    searchQuery?: string,
    topicId?: string,
  ) {
    var cusId = store.getState().customer.data?.Id;
    if (cusId) {
      let query = `@CustomerId: {${cusId}}`;

      // Add search query if provided
      if (searchQuery && searchQuery.trim()) {
        query += ` @Name: *${searchQuery.trim()}*`;
      }

      // Add topic filter if provided (support multiple topics)
      if (topicId && topicId.trim()) {
        const topicIds = topicId.split('|').filter(id => id.trim());
        if (topicIds.length > 0) {
          query += ` @TopicId: {${topicIds.join(' | ')}}`;
        }
      }

      const response = await this.flashcardController.getPatternList({
        query: query,
        page: page,
        size: size,
        pattern: {
          TopicId: ['Id', 'Name'],
        },
      });
      if (response?.code === 200) {
        return response;
      }
    }
    return null;
  }
  async getListDetailbyId(id: string) {
    var cusId = store.getState().customer.data?.Id;
    if (cusId) {
      const response = await this.flashcardDetailController.getPatternList({
        query: `@FlashCardId: {${id}}`,
        pattern: {
          FlashCardId: ['Id', 'Name', 'Description'],
        },
      });
      if (response?.code === 200) {
        return response;
      }
    }
    return null;
  }
  async getbyIdPattern(id: string) {
    const response = await this.flashcardController.getPatternList({
      query: `@Id: {${id}}`,
      pattern: {
        TopicId: ['Id', 'Name'],
      },
    });
    if (response?.code === 200) {
      const listCardId = response.data.map((item: any) => item.Id);
      const listCustomerCard = await this.customerFlascardController.group({
        reducers: `GROUPBY 1 @FlashCardId REDUCE COUNT 0 AS Count`,
        searchRaw: `@FlashCardId: {${listCardId.join(' | ')}}`,
      });
      response.data = response.data.map((item: any) => {
        const customerCard = listCustomerCard.data.find(
          (a: any) => a.FlashCardId === item.Id,
        );
        return {
          ...item,
          Count: customerCard?.Count ?? 0,
          Topic: response.Topic[0]?.Name,
        };
      });
      return response;
    }
    return null;
  }
  async getbyId(id: string) {
    const response = await this.flashcardController.getById(id);
    if (response?.code === 200) {
      return response;
    }
    return null;
  }
  async add(flashcard: any) {
    var cusId = store.getState().customer.data?.Id;
    if (cusId) {
      const response = await this.flashcardController.add([flashcard]);
      if (response?.code === 200) {
        return true;
      }
    }
    return false;
  }
  async addDetail(detail: any) {
    var cusId = store.getState().customer.data?.Id;
    if (cusId) {
      const responsedetail = await this.flashcardDetailController.add([detail]);
      if (responsedetail?.code === 200) {
        return true;
      }
    }
    return false;
  }

  async edit(flashcard: any, detail: any) {
    var cusId = store.getState().customer.data?.Id;
    if (cusId) {
      const response = await this.flashcardController.edit([flashcard]);
      const responsedetail = await this.flashcardDetailController.edit([
        ...detail,
      ]);
      if (response?.code === 200 && responsedetail?.code === 200) {
        return true;
      }
    }
    return false;
  }
  async delete(id: string) {
    var cusId = store.getState().customer.data?.Id;
    if (cusId) {
      const response = await this.flashcardController.delete([id]);
      if (response?.code === 200) {
        return true;
      }
    }
    return false;
  }
  async deleteDetail(id: string) {
    var cusId = store.getState().customer.data?.Id;
    if (cusId) {
      const response = await this.flashcardDetailController.delete([id]);
      if (response?.code === 200) {
        return true;
      }
    }
    return false;
  }
  async getTopic() {
    // group topicid with ispublic true
    const groupIds = await this.flashcardController.group({
      reducers: 'GROUPBY 0 REDUCE TOLIST 1 @TopicId AS ids',
      searchRaw: '@IsPublic:{true}',
    });
    if (groupIds?.data) {
      const response = await this.topicController.getByListId(
        groupIds?.data[0]?.ids,
      );

      if (response?.code === 200) {
        return response;
      }
    }

    return null;
  }
  //kiểm tra xem từ đã học chưa
  async checkCardIsLearned(cardId: string) {
    var cusId = store.getState().customer.data?.Id;
    if (cusId) {
      const response = await this.customerFlascardController.getListSimple({
        query: `@CustomerId: {${cusId}} @FlashCardDetailId: {${cardId}} @Status: [2]`,
        size: 1,
        returns: ['Id'],
      });
      if (response?.code === 200) {
        return response.data.length > 0;
      }
    }
    return false;
  }
  async addCustomerFlashCard(item: any) {
    var cusId = store.getState().customer.data;
    if (cusId) {
      //kiểm tra nếu dữ liệu tồn tại rồi thì ko cần add nữa.
      const check = await this.checkCardIsLearned(item.FlashCardDetailId);
      if (check) {
        return true;
      }

      const data = {
        CustomerId: cusId?.Id,
        Id: randomGID(),
        DateCreated: new Date().getTime(),
        Name: cusId.Name + '-' + item.Name,
        FlashCardId: item.FlashCardId,
        FlashCardDetailId: item.FlashCardDetailId,
        Status: 2,
      };
      const response = await this.customerFlascardController.add([data]);
      if (response?.code === 200) {
        return true;
      }
    }
    return false;
  }
}
