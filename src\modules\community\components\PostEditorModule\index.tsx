/* eslint-disable react-native/no-inline-styles */
import React, {
  useRef,
  useImperativeHandle,
  forwardRef,
  useEffect,
  useState,
} from 'react';
import {
  View,
  Text,
  TextInput,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
} from 'react-native';
import {useImagePicker} from './hooks/useImagePicker';
import {
  getFullText,
  segmentsToHTMLWithMentions,
  extractHashtags,
  extractVideoLinks,
} from './utils/textUtils';
import ImagePreview from './ImagePreview';
import Toolbar from './Toolbar';
import VideoModal from './VideoModal';
import {
  RichTextComposerProps,
  RichTextComposerRef,
  PostData,
  ImageItem,
} from './types';
import {styles} from './styles';
import {ColorThemes} from '../../../../assets/skin/colors';
import ConfigAPI from '../../../../Config/ConfigAPI';
import PickColorLine from './PickColorLine';
import EditorBackground from './EditorBackgound';
import UserMentionModal from './UserMentionModal';
import {BackgroundData} from '../../../../redux/models/PostBackground';
import {useTextSegments} from './hooks/useTextSegments';

const RichTextComposer = forwardRef<RichTextComposerRef, RichTextComposerProps>(
  (props, ref) => {
    const {
      onTextChange,
      onImagesChange,
      onDataChange,
      initialText = '',
      initialImages = [],
      initialHtml = '',
      initialImageIds = [],
      initialBackgroundData,
      maxImages = 10,
    } = props;

    const inputRef = useRef<TextInput>(null);
    const scrollViewRef = useRef<ScrollView>(null);
    const [isResetEditorBackground, setIsResetEditorBackground] =
      useState(false);
    const [editorLayout, setEditorLayout] = useState({height: 0, y: 0});
    const [backgroundData, setBackgroundData] = useState<BackgroundData>({
      Id: '0',
      Type: 999,
      Img: '',
      ColorMobile: '',
      TextColor: '',
    });
    const [showVideoModal, setShowVideoModal] = useState(false);

    // Use custom hooks
    const {
      segments,
      setSegments,
      selection,
      handleSelectionChange,
      onChangeText,
      mentionModal,
      setMentionModal,
      selectUserForMention,
      updateModalPosition,
      updateEditorInfo,
      handleModalSearch,
    } = useTextSegments(initialText, initialHtml);

    const {
      selectedImages,
      setSelectedImages,
      handlePickImages,
      handleRemoveImage,
      handleRemoveAllImages,
    } = useImagePicker(initialImages, initialImageIds, maxImages);

    // Expose methods to parent component
    useImperativeHandle(ref, () => ({
      getPostData: () => {
        const data: any = {
          text: getFullText(segments),
          segments: segments,
          images: selectedImages,
          existingImageIds: selectedImages
            .filter(img => img.existingId)
            .map(img => img.existingId as string),
        };
        if (backgroundData.Type !== 999) data.backgroundData = backgroundData;
        return data;
      },
      clearContent: () => {
        setSegments([
          {
            text: '',
            isMention: false,
            isHashtag: false,
          },
        ]);
        setSelectedImages([]);
        setBackgroundData({
          Id: '0',
          Type: 999,
          Img: '',
          ColorMobile: '',
          TextColor: '',
        });
      },
      getContentWithMentions: () => {
        return segmentsToHTMLWithMentions(segments);
      },
      getHashtags: () => {
        return extractHashtags(segments);
      },
      getVideoLinks: () => {
        const fullText = getFullText(segments);
        return extractVideoLinks(fullText);
      },
      setContent: (
        text: string,
        images: ImageItem[] = [],
        html?: string,
        imageIds?: string[],
      ) => {
        if (html) {
          // Import htmlToSegments from textUtils
          const {htmlToSegments} = require('./utils/textUtils');
          setSegments(htmlToSegments(html));
        } else {
          setSegments([{text}]);
        }

        if (images.length > 0) {
          setSelectedImages(images);
        } else if (imageIds && imageIds.length > 0) {
          setSelectedImages(
            imageIds.map((id, index) => ({
              id: `existing-${index}`,
              uri: ConfigAPI.getValidLink(id),
              path: ConfigAPI.getValidLink(id),
              mime: 'image/jpeg',
              filename: `image-${index}.jpg`,
              existingId: id,
            })),
          );
        } else {
          setSelectedImages([]);
        }
      },
      focus: () => {
        inputRef.current?.focus();
      },
    }));

    // Thêm useRef để theo dõi dữ liệu trước đó
    const prevDataRef = useRef<PostData | null>(null);

    // thông báo khi text hoặc ảnh thay đổi
    useEffect(() => {
      const fullText = getFullText(segments);

      // Tạo dữ liệu mới
      const newData = {
        text: fullText,
        segments: segments,
        images: selectedImages,
      };

      // Kiểm tra xem dữ liệu có thay đổi không
      const prevData = prevDataRef.current;
      const hasChanged =
        !prevData ||
        prevData.text !== newData.text ||
        prevData.images !== newData.images;

      // Chỉ gọi callbacks khi dữ liệu thực sự thay đổi
      if (hasChanged) {
        if (onTextChange) {
          onTextChange(fullText);
        }

        if (onImagesChange) {
          onImagesChange(selectedImages);
        }

        if (onDataChange) {
          onDataChange(newData);
        }

        // Cập nhật ref
        prevDataRef.current = newData;
      }
    }, [segments, selectedImages, onTextChange, onImagesChange, onDataChange]);

    // theo dõi selectedImages
    useEffect(() => {
      if (selectedImages.length > 0) {
        setIsResetEditorBackground(true);
        setBackgroundData({
          Id: '0',
          Type: 999,
          Img: '',
          ColorMobile: '',
          TextColor: '',
        });
      } else {
        setIsResetEditorBackground(false);
      }
    }, [selectedImages]);

    useEffect(() => {
      if (initialBackgroundData) {
        setIsResetEditorBackground(true);
        setBackgroundData({...initialBackgroundData});
      } else {
        setIsResetEditorBackground(false);
      }
    }, [initialBackgroundData]);

    const fullText = getFullText(segments);

    // Cập nhật thông tin editor khi layout thay đổi
    useEffect(() => {
      if (editorLayout.y > 0) {
        updateEditorInfo(editorLayout.y);
      }
    }, [editorLayout.y, updateEditorInfo]);

    // Cập nhật vị trí modal khi selection thay đổi (nếu modal đang hiển thị)
    useEffect(() => {
      if (editorLayout.y > 0 && selection && mentionModal.visible) {
        // Cập nhật vị trí modal dựa trên vị trí con trỏ
        updateModalPosition(editorLayout.y, selection.start, fullText);
      }
    }, [
      editorLayout.y,
      selection,
      fullText,
      mentionModal.visible,
      updateModalPosition,
    ]);

    // Loại bỏ toggle format - không cần thiết nữa

    // Chọn màu nền cho bài post
    const handleChooseBackgroundData = (color: BackgroundData) => {
      handleRemoveAllImages();
      setBackgroundData(color);
    };

    // Xử lý hiển thị modal video
    const handleShowVideoModal = () => {
      setShowVideoModal(true);
    };

    // Xử lý thêm link video vào text
    const handleAddVideoLink = (link: string) => {
      const currentText = getFullText(segments);
      const newText = currentText + (currentText ? '\n\n' : '') + link;
      setSegments([
        {
          text: newText,
          isMention: false,
          isHashtag: false,
        },
      ]);
    };

    const renderEditorDefault = () => {
      return (
        <View style={styles.editorContainer}>
          <ScrollView
            ref={scrollViewRef}
            style={styles.scrollArea}
            keyboardShouldPersistTaps="always"
            onLayout={event => {
              const {height, y} = event.nativeEvent.layout;
              setEditorLayout({height, y});
            }}>
            <TextInput
              ref={inputRef}
              value={fullText}
              onChangeText={onChangeText}
              onSelectionChange={handleSelectionChange}
              multiline
              selection={selection}
              placeholder={fullText ? "" : "Bạn đang nghĩ gì ?"}
              placeholderTextColor={
                ColorThemes.light.Neutral_Text_Color_Subtitle
              }
              cursorColor={ColorThemes.light.Neutral_Text_Color_Body}
              style={[
                styles.simpleTextInput,
                // Làm text trong suốt để tránh double text khi có highlight overlay
                {color: 'transparent'}
              ]}
              autoCorrect={false}
              autoCapitalize="sentences"
              keyboardType="default"
              textAlignVertical="top"
            />

            {/* Hiển thị text với highlight cho hashtag, mention và video link */}
            <View style={styles.highlightOverlay} pointerEvents="none">
              <Text style={styles.highlightText}>
                {segments.map((seg: any, idx: number) => {
                  // Kiểm tra xem có phải video link không
                  const isVideoLink =
                    seg.text && /^https?:\/\/.+/.test(seg.text.trim());

                  return (
                    <Text
                      key={idx}
                      style={{
                        fontSize: 16,
                        lineHeight: 24,
                        fontFamily: 'NotoSansJP-Regular',
                        color: seg.isHashtag
                          ? '#007AFF'
                          : seg.isMention
                          ? '#007AFF'
                          : isVideoLink
                          ? '#007AFF'
                          : ColorThemes.light.Neutral_Text_Color_Body,
                        backgroundColor: 'transparent',
                        textDecorationLine:
                          seg.isMention || isVideoLink ? 'underline' : 'none',
                      }}>
                      {seg.text}
                    </Text>
                  );
                })}
              </Text>
            </View>
          </ScrollView>
        </View>
      );
    };

    const renderEditorBackground = () => {
      return (
        <EditorBackground
          value={fullText}
          background={backgroundData}
          onChangeText={onChangeText}
        />
      );
    };

    return (
      <SafeAreaView style={styles.container}>
        <KeyboardAvoidingView
          style={{flex: 1}}
          behavior={Platform.OS === 'ios' ? 'padding' : undefined}
          keyboardVerticalOffset={Platform.OS === 'ios' ? 120 : 0}>
          {/* Editor */}
          {backgroundData.Type === 999
            ? renderEditorDefault()
            : renderEditorBackground()}

          {/* Pick Color Line */}
          <PickColorLine
            initialBackgroundId={initialBackgroundData?.Id}
            onChoose={handleChooseBackgroundData}
            isReset={isResetEditorBackground}
          />

          {/* Image Preview Section */}
          <ImagePreview
            selectedImages={selectedImages}
            handleRemoveImage={handleRemoveImage}
            handlePickImages={handlePickImages}
            maxImages={maxImages}
          />
          {/* Toolbar */}
          <Toolbar
            handlePickImages={handlePickImages}
            handleShowYouTubeModal={handleShowVideoModal}
          />
        </KeyboardAvoidingView>

        {/* User Mention Modal */}
        <UserMentionModal
          mentionModal={mentionModal}
          onSelectUser={selectUserForMention}
          onClose={() =>
            setMentionModal((prev: any) => ({...prev, visible: false}))
          }
          onSearchChange={handleModalSearch}
        />

        {/* Video Modal */}
        <VideoModal
          visible={showVideoModal}
          onClose={() => setShowVideoModal(false)}
          onAddLink={handleAddVideoLink}
        />
      </SafeAreaView>
    );
  },
);

export default RichTextComposer;
