/* eslint-disable react-native/no-inline-styles */
import React, {useRef, useState, useEffect} from 'react';
import {View, Alert, Text, StyleSheet} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {useRoute} from '@react-navigation/native';
import {useDispatch} from 'react-redux';

import {ColorThemes} from '../../../assets/skin/colors';
import {TypoSkin} from '../../../assets/skin/typography';
import ScreenHeader from '../../../Screen/Layout/header';
import {AppButton, FBottomSheet, Winicon} from 'wini-mobile-components';
import {navigateBack} from '../../../router/router';
import RichTextComposer, {
  RichTextComposerRef,
  PostData,
} from '../components/PostEditor';
import {GroupPostsActions} from '../reducers/groupPostsReducer';
import {newsFeedActions} from '../reducers/newsFeedReducer';
import {myFeedActions} from '../reducers/MyFeedReducer';
import {DataController} from '../../../base/baseController';
import {BaseDA} from '../../../base/BaseDA';
import store, {AppDispatch} from '../../../redux/store/store';
import {useSelectorCustomerState} from '../../../redux/hook/customerHook';
import {randomGID} from '../../../utils/Utils';
import {fetchPostBackgrounds} from '../../../redux/reducers/postBackgroundReducer';
import {PostStatus} from '../components/PostEditorModule/utils/textUtils';

interface RouteParams {
  groupId?: string;
  editPost?: any;
}

const CreatePost = () => {
  const route = useRoute<any>();
  const {groupId, editPost}: RouteParams = route.params || {}; // Lấy thông tin post cần edit nếu có

  const bottomSheetRef = useRef<any>(null);
  const richTextRef = useRef<RichTextComposerRef>(null);
  const dispatch: AppDispatch = useDispatch();
  const customer = useSelectorCustomerState().data;

  const isEditMode = !!editPost; // Kiểm tra xem có phải mode edit không

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [postData, setPostData] = useState<PostData>({
    text: '',
    segments: [],
    images: [],
    html: undefined,
    backgroundData: null,
  });

  // Xử lý dữ liệu ban đầu nếu đang ở chế độ edit
  useEffect(() => {
    if (isEditMode && richTextRef.current && editPost) {
      const imageIds = parseImageIds(editPost.Img);
      // Thiết lập nội dung cho RichTextComposer
      richTextRef.current.setContent('', [], editPost.Content, imageIds);
    }
  }, [isEditMode, editPost]);

  useEffect(() => {
    dispatch(fetchPostBackgrounds());
  }, []);

  // Xử lý danh sách ID ảnh từ string hoặc array
  const parseImageIds = (imgData: any): string[] => {
    if (!imgData) return [];

    if (typeof imgData === 'string') {
      // Nếu Img là chuỗi chứa nhiều ID ngăn cách bởi dấu phẩy
      return imgData.includes(',')
        ? imgData.split(',').filter((img: string) => img.trim() !== '')
        : // Nếu Img là một ID duy nhất
        imgData.trim() !== ''
        ? [imgData.trim()]
        : [];
    }

    // Nếu Img là mảng
    if (Array.isArray(imgData)) {
      return imgData.filter((img: string) => img && img.trim() !== '');
    }

    return [];
  };

  // Callback khi dữ liệu từ RichTextComposer thay đổi
  const handlePostDataChange = (data: PostData) => {
    setPostData(data);
  };

  // Xử lý upload ảnh mới (nếu có)
  const uploadNewImages = async (images: any[]): Promise<string[]> => {
    const newImages = images.filter(img => !img.existingId);
    if (newImages.length === 0) return [];

    const fileToUpload = newImages.map((img: any) => ({
      uri: img.path,
      type: img.mime,
      name: img.filename ?? 'new file img',
    }));

    const uploadResult = await BaseDA.uploadFiles(fileToUpload);
    return uploadResult?.length > 0
      ? uploadResult.map((item: any) => item.Id)
      : [];
  };

  // Lấy danh sách ID ảnh đã tồn tại
  const getExistingImageIds = (images: any[]): string[] => {
    return images
      .filter(img => img.existingId)
      .map(img => img.existingId as string);
  };

  // Cập nhật state trong Redux
  const updatePostInStore = (updatedPost: any) => {
    const state = store.getState();

    // Update in newsFeed
    const newfeed = state.newsFeed.data.find(
      (item: any) => item.Id === updatedPost.Id,
    );
    if (newfeed) {
      dispatch(newsFeedActions.updatePost(updatedPost));
    }

    // Update in myFeed
    const myfeed = state.myFeed.data.find(
      (item: any) => item.Id === updatedPost.Id,
    );
    if (myfeed) {
      dispatch(myFeedActions.updatePost(updatedPost));
    }

    // Update in groupPosts
    if (updatedPost.GroupId) {
      const groupPosts = state.groupPosts.byGroupId[updatedPost.GroupId]?.posts;
      const groupfeed = groupPosts?.find(
        (item: any) => item.Id === updatedPost.Id,
      );
      if (groupfeed) {
        dispatch(GroupPostsActions.updatePost(updatedPost));
      }
    }
  };

  // Nếu đang edit, cập nhật bài đăng hiện có
  const handleEditPost = async (editorData: PostData): Promise<void> => {
    // Kết hợp ID ảnh đã tồn tại và ID ảnh mới upload
    const [newImageIds, existingImageIds] = await Promise.all([
      uploadNewImages(editorData.images),
      Promise.resolve(getExistingImageIds(editorData.images)),
    ]);

    const allImageIds = [...existingImageIds, ...newImageIds];

    // Lấy content với mentions, hashtags và video links
    const contentWithMentions =
      richTextRef.current?.getContentWithMentions() || editorData.text;
    const hashtags = richTextRef.current?.getHashtags() || '';
    const videoLinks = richTextRef.current?.getVideoLinks() || '';

    const updatedPost = {
      ...editPost,
      Content: `<p>${contentWithMentions}</p>`,
      ListTag: hashtags,
      LinkVideo: videoLinks,
      Img: allImageIds.join(','),
      DateModified: new Date().getTime(),
      PostBackgroundMId: editorData.backgroundData?.Id,
      PostBackgroundM: editorData.backgroundData,
    };

    const postToApi = {
      ...updatedPost,
    };

    delete postToApi.PostBackgroundM;
    delete postToApi.relativeUser;
    delete postToApi.Likes;
    delete postToApi.CustomerLiked;

    // Gọi API cập nhật bài đăng
    const postController = new DataController('Posts');
    const result = await postController.edit([postToApi]);

    if (result.code === 200) {
      updatePostInStore(updatedPost);
    } else {
      throw new Error('Cập nhật bài đăng thất bại');
    }
  };

  // Nếu đang tạo mới, thêm bài đăng mới
  const handleCreatePost = async (editorData: PostData): Promise<void> => {
    const newImageIds = await uploadNewImages(editorData.images);
    const existingImageIds = getExistingImageIds(editorData.images);
    const allImageIds = [...existingImageIds, ...newImageIds];

    // Lấy content với mentions, hashtags và video links
    const contentWithMentions =
      richTextRef.current?.getContentWithMentions() || editorData.text;
    const hashtags = richTextRef.current?.getHashtags() || '';
    const videoLinks = richTextRef.current?.getVideoLinks() || '';

    const newPost: any = {
      Content: `<p>${contentWithMentions}</p>`,
      ListTag: hashtags,
      LinkVideo: videoLinks,
      Img: allImageIds.join(','),
      Id: randomGID(),
      CustomerId: customer.Id,
      GroupId: groupId,
      DateCreated: new Date().getTime(),
      IsHidden: false,
      Status: PostStatus.public,
      PostBackgroundMId: editorData.backgroundData?.Id,
      PostBackgroundM: editorData.backgroundData,
    };

    const postWithUser = {
      ...newPost,
      Likes: 0,
      IsLike: false,
      Comment: 0,
      IsBookmark: false,
      relativeUser: {
        image: customer.AvatarUrl,
        title: customer.Name,
        subtitle: 'Just now',
      },
    };

    if (!groupId) {
      dispatch(newsFeedActions.addPost(newPost));
      dispatch(myFeedActions.addPostNoCall(postWithUser));
    } else {
      dispatch(GroupPostsActions.addPost(newPost));
      dispatch(myFeedActions.addPostNoCall(postWithUser));
      dispatch(newsFeedActions.addPostNoCall(postWithUser));
    }
  };

  // Hàm xử lý khi submit form
  const handleSubmit = async () => {
    if (isSubmitting) return;

    try {
      setIsSubmitting(true);
      // Lấy dữ liệu từ RichTextComposer
      const editorData = richTextRef.current?.getPostData() || postData;

      if (isEditMode) {
        await handleEditPost(editorData);
      } else {
        await handleCreatePost(editorData);
      }

      richTextRef.current?.clearContent();
      navigateBack();
    } catch (error) {
      console.error('Submit error:', error);
      Alert.alert('Lỗi', 'Đã xảy ra lỗi khi xử lý bài đăng. Vui lòng thử lại.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Check if submit button should be disabled
  const isSubmitDisabled =
    isSubmitting || (!postData.text && postData.images.length === 0);

  return (
    <SafeAreaView style={styles.container}>
      <FBottomSheet ref={bottomSheetRef} />

      <ScreenHeader
        title={isEditMode ? 'Chỉnh sửa bài đăng' : 'Tạo bài đăng'}
        style={styles.header}
        action={
          <AppButton
            title={isEditMode ? 'Cập nhật' : 'Đăng'}
            backgroundColor={ColorThemes.light.transparent}
            textColor={ColorThemes.light.Primary_Color_Main}
            borderColor="transparent"
            containerStyle={styles.submitButtonContainer}
            onPress={isSubmitDisabled ? undefined : handleSubmit}
            textStyle={{
              ...styles.submitButtonText,
              color: isSubmitDisabled
                ? ColorThemes.light.Neutral_Text_Color_Subtitle
                : ColorThemes.light.Primary_Color_Main,
            }}
          />
        }
        backIcon={
          <View style={styles.backIconContainer}>
            <Winicon src="outline/user interface/e-remove" size={20} />
            <Text style={styles.backIconText}>Đóng</Text>
          </View>
        }
        onBack={navigateBack}
      />

      {/* Sử dụng RichTextComposer */}
      <RichTextComposer
        ref={richTextRef}
        onDataChange={handlePostDataChange}
        initialText={editPost?.Content}
        maxImages={10}
        initialBackgroundData={editPost?.PostBackgroundM}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
  },
  header: {
    backgroundColor: ColorThemes.light.transparent,
    borderBottomColor: ColorThemes.light.Neutral_Background_Color_Main,
    borderBottomWidth: 0.5,
    shadowColor: 'rgba(0, 0, 0, 0.03)',
    shadowOffset: {width: 0, height: 4},
    shadowRadius: 20,
    elevation: 20,
    shadowOpacity: 1,
  },
  submitButtonContainer: {
    padding: 4,
    marginRight: 16,
  },
  submitButtonText: {
    ...TypoSkin.buttonText3,
  },
  backIconContainer: {
    paddingVertical: 8,
    paddingRight: 16,
    gap: 4,
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'baseline',
  },
  backIconText: {
    ...TypoSkin.heading8,
    color: ColorThemes.light.Neutral_Text_Color_Title,
  },
});

export default CreatePost;
