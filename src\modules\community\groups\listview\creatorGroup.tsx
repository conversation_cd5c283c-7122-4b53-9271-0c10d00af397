/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/no-unstable-nested-components */
/* eslint-disable react-native/no-inline-styles */
import React, {useEffect, useRef, useState} from 'react';
import {
  View,
  Text,
  FlatList,
  RefreshControl,
  TouchableOpacity,
} from 'react-native';
import {ColorThemes} from '../../../../assets/skin/colors';
import {TypoSkin} from '../../../../assets/skin/typography';
import {AppButton, FDialog, SkeletonImage} from 'wini-mobile-components';
import {useTranslation} from 'react-i18next';
import {useNavigation} from '@react-navigation/native';
import {RootScreen} from '../../../../router/router';
import EmptyPage from '../../../../Screen/emptyPage';
import {ScrollView} from 'react-native-gesture-handler';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import {useDispatch, useSelector} from 'react-redux';
import {AppDispatch} from '../../../../redux/store/store';
import ConfigAPI from '../../../../Config/ConfigAPI';
import {myGroupsActions} from '../../reducers/myGroupsReducer';
import FastImage from 'react-native-fast-image';

interface Props {
  horizontal?: boolean;
  titleList?: string;
  isSeeMore?: boolean;
  id: string;
  onPressSeeMore?: () => void;
  onRefresh?: () => void;
}
export default function CreatorGroup(props: Props) {
  //   const [data, setData] = useState<Array<any>>([]);
  const {t} = useTranslation();
  const navigation = useNavigation<any>();
  const dialogRef = useRef<any>(null);
  const dispatch: AppDispatch = useDispatch();
  const {groups, isLoading} = useSelector((state: any) => state.myGroups);
  useEffect(() => {
    dispatch(myGroupsActions.getMyGroups());
  }, []);
  return (
    groups?.length > 0 && (
      <View
        style={{
          height: props.horizontal ? 175 : undefined,
          marginBottom: 24,
        }}>
        <FDialog ref={dialogRef} />
        {props.titleList ? (
          <View
            style={{
              alignItems: 'center',
              justifyContent: 'space-between',
              paddingHorizontal: 16,
              paddingBottom: 16,
              flexDirection: 'row',
              backgroundColor:
                ColorThemes.light.Neutral_Background_Color_Absolute,
            }}>
            <Text
              style={{
                ...TypoSkin.heading5,
                color: ColorThemes.light.Neutral_Text_Color_Title,
              }}>
              {props.titleList}
            </Text>
            {props.isSeeMore ? (
              <AppButton
                title={'See all'}
                containerStyle={{
                  justifyContent: 'flex-start',
                  alignSelf: 'baseline',
                }}
                backgroundColor={'transparent'}
                textStyle={TypoSkin.buttonText3}
                borderColor="transparent"
                suffixIconSize={16}
                suffixIcon={'outline/arrows/circle-arrow-right'}
                onPress={props.onPressSeeMore}
                textColor={ColorThemes.light.Info_Color_Main}
              />
            ) : null}
          </View>
        ) : null}
        <FlatList
          data={groups}
          showsHorizontalScrollIndicator={false}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{
            gap: 16,
            backgroundColor:
              ColorThemes.light.Neutral_Background_Color_Absolute,
            paddingHorizontal: 16,
          }}
          renderItem={({item, index}) => {
            return (
              <TouchableOpacity
                style={{
                  borderRadius: 8,
                  alignContent: 'center',
                  alignItems: 'center',
                  gap: 16,
                  borderColor: ColorThemes.light.Neutral_Border_Color_Main,
                  borderWidth: 1,
                  paddingVertical: 16,
                  paddingHorizontal: 16,
                  width: 155,
                }}
                onPress={() => {
                  navigation.push(RootScreen.GroupIndex, {Id: item.Id});
                }}>
                <FastImage
                  key={item.Thumb}
                  source={
                    item.Thumb
                      ? {
                          uri: item.Thumb
                            ? ConfigAPI.getValidLink(item.Thumb)
                            : 'https://placehold.co/56/FFFFFF/000000/png',
                        }
                      : require('../../../../assets/appstore.png')
                  }
                  style={{
                    height: 40,
                    width: 40,
                    borderRadius: 100,
                    backgroundColor:
                      ColorThemes.light.Neutral_Background_Color_Main,
                  }}
                />
                <Text
                  style={{
                    ...TypoSkin.heading7,
                    color: ColorThemes.light.Neutral_Text_Color_Title,
                  }}
                  numberOfLines={1}>
                  {item.Name}
                </Text>
              </TouchableOpacity>
            );
          }}
          style={{width: '100%', height: '100%'}}
          keyExtractor={item => item.Id?.toString()}
          horizontal={true}
          ListEmptyComponent={() => {
            if (isLoading) {
              return (
                <ScrollView
                  horizontal
                  showsHorizontalScrollIndicator={false}
                  contentContainerStyle={{gap: 16, padding: 16}}>
                  <SkeletonPlaceCard />
                  <SkeletonPlaceCard />
                  <SkeletonPlaceCard />
                </ScrollView>
              );
            }
            return <EmptyPage title={t('nodata')} />;
          }}
        />
      </View>
    )
  );
}

export function SkeletonPlaceCard() {
  return (
    <SkeletonPlaceholder backgroundColor="#f0f0f0" highlightColor="#e0e0e0">
      <View
        style={{
          width: 120,
          padding: 16,
          borderRadius: 8,
          borderWidth: 1,
          borderColor: '#e0e0e0',
          alignItems: 'center',
          gap: 12,
        }}>
        {/* Icon/Image placeholder */}
        <View
          style={{
            width: 40,
            height: 40,
            borderRadius: 20,
          }}
        />

        {/* Title placeholder */}
        <View
          style={{
            width: '80%',
            height: 16,
            borderRadius: 4,
          }}
        />
      </View>
    </SkeletonPlaceholder>
  );
}
